<?php

namespace App\Helpers;

class ClassifiedPart
{
    /**
     * Classify part demand based on stock-out history and current stock.
     *
     * @param array<int> $stockOutHistory  Monthly/Weekly stock-out quantities, e.g., [2, 3, 1, 0, 5, 6]
     * @param int        $stockNow         Current stock on hand
     *
     * @return string                      One of: 'High Demand', 'Stable', 'Low Demand', 'Seasonal', 'Overstocked', 'Uncategorized', 'Data Insufficient'
     */
    public static function classify(array $stockOutHistory, int $stockNow): string
    {
        // Constants (thresholds can be adjusted here)
        $MIN_REQUIRED_MONTHS = 4;
        $HIGH_DEMAND_AVG = 10;
        $STABLE_AVG_MIN = 1;
        $STABLE_AVG_MAX = 3;
        $LOW_DEMAND_AVG_MAX = 3;
        $OVERSTOCKED_MULTIPLIER = 6;

        $months = count($stockOutHistory);

        if ($months < $MIN_REQUIRED_MONTHS) {
            return 'Data Insufficient';
        }

        $averageUsage = array_sum($stockOutHistory) / $months;

        $variance = array_sum(array_map(
            fn($v) => pow($v - $averageUsage, 2),
            $stockOutHistory
        )) / $months;

        $stdDeviation = sqrt($variance);

        $usedMonths = count(array_filter($stockOutHistory, fn($v) => $v > 0));
        $usedPercent = $usedMonths / $months;

        // Classification Rules
        if (
            $averageUsage >= $HIGH_DEMAND_AVG &&
            $stdDeviation <= 2 &&
            $usedPercent > 0.8
        ) {
            return 'High Demand';
        }

        if (
            $averageUsage >= $STABLE_AVG_MIN &&
            $averageUsage <= $STABLE_AVG_MAX &&
            $usedPercent > 0.8 &&
            $stdDeviation <= 1.5
        ) {
            return 'Stable';
        }

        if (
            $usedPercent >= 0.7 &&
            $averageUsage < $LOW_DEMAND_AVG_MAX &&
            $stockNow < ($averageUsage * 2)
        ) {
            return 'Low Demand';
        }

        if (
            $stdDeviation > 5 &&
            $usedPercent <= 0.4 &&
            $averageUsage > 2
        ) {
            return 'Seasonal';
        }

        if (
            $stockNow > ($averageUsage * $OVERSTOCKED_MULTIPLIER) &&
            $averageUsage <= 1
        ) {
            return 'Overstocked';
        }

        return 'Uncategorized';
    }
}
